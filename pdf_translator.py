#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF翻译工具
将PDF文档翻译成中文并生成原文+译文的整合版本
"""

import os
import sys
import time
import re
import json
from typing import List, Tuple
import pdfplumber
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import requests
import urllib.parse

class PDFTranslator:
    def __init__(self, input_pdf_path: str, output_pdf_path: str = None):
        self.input_pdf_path = input_pdf_path
        self.output_pdf_path = output_pdf_path or self.input_pdf_path.replace('.pdf', '_translated.pdf')
        self.setup_fonts()
        
    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 尝试注册系统中文字体
            font_paths = [
                'C:/Windows/Fonts/simsun.ttc',  # 宋体
                'C:/Windows/Fonts/simhei.ttf',  # 黑体
                'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        print(f"成功注册字体: {font_path}")
                        break
                    except Exception as e:
                        print(f"注册字体失败 {font_path}: {e}")
                        continue
            else:
                print("警告: 未找到中文字体，将使用默认字体")
                
        except Exception as e:
            print(f"字体设置错误: {e}")
    
    def extract_text_from_pdf(self) -> List[str]:
        """从PDF中提取文本"""
        print("正在提取PDF文本...")
        text_blocks = []
        
        try:
            with pdfplumber.open(self.input_pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    print(f"处理第 {page_num}/{len(pdf.pages)} 页")
                    text = page.extract_text()
                    if text:
                        # 清理文本
                        text = self.clean_text(text)
                        if text.strip():
                            text_blocks.append(f"[第{page_num}页]\n{text}")
                            
        except Exception as e:
            print(f"PDF文本提取错误: {e}")
            return []
            
        print(f"成功提取 {len(text_blocks)} 个文本块")
        return text_blocks
    
    def clean_text(self, text: str) -> str:
        """清理提取的文本"""
        if not text:
            return ""
            
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除页眉页脚等常见模式
        text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)
        # 保留段落结构
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
    
    def translate_text(self, text: str, max_retries: int = 3) -> str:
        """翻译文本到中文"""
        if not text.strip():
            return ""

        for attempt in range(max_retries):
            try:
                # 分段翻译长文本
                if len(text) > 4000:
                    return self.translate_long_text(text)

                # 使用免费的翻译API
                result = self.translate_with_api(text)
                if result:
                    return result

            except Exception as e:
                print(f"翻译尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 等待后重试

        return f"[翻译失败] {text[:100]}..."

    def translate_with_api(self, text: str) -> str:
        """使用免费翻译API"""
        try:
            # 使用MyMemory翻译API (免费)
            url = "https://api.mymemory.translated.net/get"
            params = {
                'q': text[:500],  # 限制长度
                'langpair': 'en|zh'
            }

            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('responseStatus') == 200:
                    return data['responseData']['translatedText']

            # 备用方案：使用简单的替换翻译
            return self.simple_translate(text)

        except Exception as e:
            print(f"API翻译失败: {e}")
            return self.simple_translate(text)

    def simple_translate(self, text: str) -> str:
        """简单的翻译替换（备用方案）"""
        # 这是一个简化的翻译方案，实际使用中可以扩展
        common_translations = {
            'Abstract': '摘要',
            'Introduction': '引言',
            'Conclusion': '结论',
            'References': '参考文献',
            'Figure': '图',
            'Table': '表',
            'Keywords': '关键词',
            'and': '和',
            'the': '',
            'of': '的',
            'in': '在',
            'for': '为了',
            'with': '与',
            'by': '通过',
            'from': '从',
            'to': '到',
            'is': '是',
            'are': '是',
            'was': '是',
            'were': '是'
        }

        translated = text
        for en, zh in common_translations.items():
            translated = translated.replace(en, zh)

        return f"[简化翻译] {translated}"
    
    def translate_long_text(self, text: str) -> str:
        """翻译长文本（分段处理）"""
        sentences = text.split('. ')
        translated_parts = []
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk + sentence) < 500:  # 减小块大小
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    translated = self.translate_with_api(current_chunk.strip())
                    translated_parts.append(translated)
                    time.sleep(1)  # 避免API限制
                current_chunk = sentence + ". "

        if current_chunk:
            translated = self.translate_with_api(current_chunk.strip())
            translated_parts.append(translated)

        return " ".join(translated_parts)

    def create_bilingual_pdf(self, text_blocks: List[str], translations: List[str]):
        """创建双语PDF文档"""
        print("正在生成双语PDF...")

        doc = SimpleDocTemplate(
            self.output_pdf_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )

        # 创建样式
        styles = getSampleStyleSheet()

        # 英文原文样式
        original_style = ParagraphStyle(
            'OriginalText',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=12,
            fontName='Times-Roman'
        )

        # 中文译文样式
        try:
            chinese_style = ParagraphStyle(
                'ChineseText',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=12,
                fontName='ChineseFont'
            )
        except:
            chinese_style = ParagraphStyle(
                'ChineseText',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=12,
                fontName='Times-Roman'
            )

        # 标题样式
        title_style = ParagraphStyle(
            'Title',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=20,
            alignment=1  # 居中
        )

        story = []

        # 添加标题
        story.append(Paragraph("PDF翻译文档 (原文+中文译文)", title_style))
        story.append(Spacer(1, 20))

        # 添加内容
        for i, (original, translation) in enumerate(zip(text_blocks, translations)):
            # 原文
            story.append(Paragraph(f"<b>原文:</b>", original_style))
            story.append(Paragraph(original.replace('\n', '<br/>'), original_style))
            story.append(Spacer(1, 10))

            # 译文
            story.append(Paragraph(f"<b>中文译文:</b>", chinese_style))
            story.append(Paragraph(translation.replace('\n', '<br/>'), chinese_style))
            story.append(Spacer(1, 20))

            # 每5个文本块后添加分页
            if (i + 1) % 5 == 0:
                story.append(PageBreak())

        try:
            doc.build(story)
            print(f"双语PDF已生成: {self.output_pdf_path}")
        except Exception as e:
            print(f"PDF生成错误: {e}")

    def translate_pdf(self):
        """主要的翻译流程"""
        print(f"开始翻译PDF: {self.input_pdf_path}")

        # 1. 提取文本
        text_blocks = self.extract_text_from_pdf()
        if not text_blocks:
            print("错误: 无法从PDF中提取文本")
            return False

        # 2. 翻译文本
        print("开始翻译文本...")
        translations = []

        for i, text_block in enumerate(text_blocks, 1):
            print(f"翻译进度: {i}/{len(text_blocks)}")
            translation = self.translate_text(text_block)
            translations.append(translation)
            time.sleep(1)  # 避免API限制

        # 3. 生成双语PDF
        self.create_bilingual_pdf(text_blocks, translations)

        print("翻译完成!")
        return True


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python pdf_translator.py <PDF文件路径> [输出文件路径]")
        return

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2] if len(sys.argv) > 2 else None

    if not os.path.exists(input_pdf):
        print(f"错误: 文件不存在 - {input_pdf}")
        return

    translator = PDFTranslator(input_pdf, output_pdf)
    translator.translate_pdf()


if __name__ == "__main__":
    main()
