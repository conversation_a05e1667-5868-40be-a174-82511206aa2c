# PDF翻译工具

这是一个Python工具，可以将PDF文档翻译成中文并生成包含原文和译文的整合版PDF。

## 功能特点

- 自动提取PDF文本内容
- 使用Google翻译API将文本翻译成中文
- 生成包含原文和中文译文的双语PDF文档
- 支持中文字体显示
- 自动处理长文本分段翻译

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法
```bash
python pdf_translator.py <输入PDF文件路径> [输出PDF文件路径]
```

### 示例
```bash
# 翻译PDF文件，输出文件名自动生成
python pdf_translator.py "input.pdf"

# 翻译PDF文件，指定输出文件名
python pdf_translator.py "input.pdf" "output_translated.pdf"
```

## 输出格式

生成的PDF文档包含：
- 文档标题
- 每个文本块的原文
- 对应的中文译文
- 清晰的格式分隔

## 注意事项

1. **网络连接**：需要稳定的网络连接访问Google翻译服务
2. **API限制**：Google翻译有使用频率限制，脚本已内置延时处理
3. **字体支持**：自动检测并使用系统中文字体
4. **文件格式**：仅支持可提取文本的PDF文件

## 文件说明

- `pdf_translator.py` - 主要的翻译脚本
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档

## 已完成的翻译

✅ `JTPES-2024-4-6_3.pdf` → `JTPES-2024-4-6_3_translated.pdf`

原文档已成功翻译并生成双语版本PDF文档。
