分析

python
始终显示详情

复制
from docx import Document
import re

# Load the document
doc_path = "/mnt/data/manuscript61.docx"
doc = Document(doc_path)

# Define punctuation marks that should be followed by a space
punctuation = ['.', ',', ':', ';', '!', '?']

# Compile a regex pattern for finding punctuation not followed by a space or newline
pattern = re.compile(r'([.,:;!?])(?![\s\n])')

# Function to fix punctuation spacing and bold figure captions
def fix_text_and_format(paragraph):
    # Fix spacing after punctuation
    original_text = paragraph.text
    fixed_text = pattern.sub(r'\1 ', original_text)

    # Bold figure captions like "Figure 1", "Figure 2a", etc.
    new_runs = []
    for run in paragraph.runs:
        text = pattern.sub(r'\1 ', run.text)
        run.text = text
        match = re.match(r'(Figure\s+\d+[a-zA-Z]?)', run.text)
        if match:
            run.bold = True
    paragraph.text = fixed_text

# Apply the function to all paragraphs in the document
for para in doc.paragraphs:
    fix_text_and_format(para)

# Save the corrected document
output_path = "/mnt/data/manuscript61_corrected.docx"
doc.save(output_path)

output_path